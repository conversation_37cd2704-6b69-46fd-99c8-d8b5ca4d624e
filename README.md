# Evaluations Engine

This API houses the need resources for TESS applications to communicate with the evaluation module.

## Resources

### Get Evaluation

The endpoint `GET evaluation/:id` will information about the requested evaluation along with a list of questions for the evaluation. The question list may or may not include all the questions associated with this evaluation. The questions returned are chosen based on the evaluations settings.

### Initialize Checklist Session

The endpoint `POST checklist/init/:id` is used to initialize a session for launching a checklist. The endpoint requires that you pass in the body a list of user ids. The endpoint will respond back with a unique session id, this id will be active for 5 minutes after this point a new session will need to be generated if the session has not been redeemed. The session can only be redeemed one time.

<details>
  <summary>Request Body</summary>

  ```json
  {
    "userIds": ["id-1", "id-2"]
  }
  ```
</details>

### Get Checklist Session Data

The endpoint `GET checklist/session-data/:id` will return back information need to initialize the checklist client. This endpoint will return the Id of the checklist to launch and the list of user ids given at session initialization.

<details>
  <summary>Response Body</summary>

  ```json
  {
    "checklistId": "id-of-the-checklist",
    "userIds": ["user-id-1"]
  }
  ```
</details>
