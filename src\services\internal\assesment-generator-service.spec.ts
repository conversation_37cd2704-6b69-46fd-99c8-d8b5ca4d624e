import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import { v4 as uuid } from 'uuid'
import { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import { QuestionWithOptions } from '../../models/internal/question-with-options.js'

type QuestionWithDisplayInfo = QuestionWithOptions & {
    DisplayIndex?: number
    SectionDisplayIndex?: number
}

describe('Assessment Generator Service', () => {
    before(() => logger.init({ level: 'silly' }))
    afterEach(() => Sinon.restore())

    const mockEvaluation: CurrentEvaluationView = {
        Id: uuid(),
        Version: 1,
        EvaluationTypeId: 1,
        RandomizeQuestions: false
    } as CurrentEvaluationView



    it('processes fixed questions without QuestionSetId', async () => {
        const mockEvaluationQuestions = [
            {
                QuestionId: uuid(),
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 1
            }
        ]

        const mockFixedQuestions = [
            { Id: uuid(), Version: 1 }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockEvaluationQuestions })
            .onSecondCall().resolves({ recordset: mockFixedQuestions })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)
        
        expect(result).to.have.length(1)
        expect(result[0].DisplayIndex).to.equal(1)
        expect(result[0].SectionDisplayIndex).to.equal(1)
    })

    it('processes dynamic questions with QuestionSetId', async () => {
        const mockEvaluationQuestions = [
            {
                QuestionSetId: uuid(),
                QuestionSetVersion: 1,
                SubsetSize: 2,
                DisplayIndex: 2,
                SectionDisplayIndex: 1
            }
        ]

        const mockDynamicQuestions = [
            { Id: uuid(), Version: 1 },
            { Id: uuid(), Version: 1 },
            { Id: uuid(), Version: 1 }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockEvaluationQuestions })
            .onSecondCall().resolves({ recordset: mockDynamicQuestions })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)
        
        expect(result).to.have.length(2) // SubsetSize limits to 2
        expect(result[0].DisplayIndex).to.equal(2)
        expect(result[0].SectionDisplayIndex).to.equal(1)
    })

    it('sorts questions by section DisplayIndex then question DisplayIndex', async () => {
        const mockEvaluationQuestions = [
            {
                QuestionId: 'q1',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 2,
                SectionDisplayIndex: 2
            },
            {
                QuestionId: 'q2',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 1
            },
            {
                QuestionId: 'q3',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 2
            }
        ]

        const mockFixedQuestions = [
            { Id: 'q1', Version: 1 },
            { Id: 'q2', Version: 1 },
            { Id: 'q3', Version: 1 }
        ]

        let callCount = 0
        const queryStub = Sinon.stub().callsFake(() => {
            callCount++
            if (callCount === 1) {
                return Promise.resolve({ recordset: mockEvaluationQuestions })
            } else if (callCount === 2) {
                return Promise.resolve({ recordset: [mockFixedQuestions[0]] }) // q1
            } else if (callCount === 3) {
                return Promise.resolve({ recordset: [mockFixedQuestions[1]] }) // q2
            } else if (callCount === 4) {
                return Promise.resolve({ recordset: [mockFixedQuestions[2]] }) // q3
            }
            return Promise.resolve({ recordset: [] })
        })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)

        expect(result).to.have.length(3)
        // Section 1 should come first
        expect(result[0].Id).to.equal('q2') // Section 1, DisplayIndex 1
        // Section 2 questions should be sorted by DisplayIndex
        expect(result[1].Id).to.equal('q3') // Section 2, DisplayIndex 1
        expect(result[2].Id).to.equal('q1') // Section 2, DisplayIndex 2
    })

    it('randomizes questions within sections when RandomizeQuestions is true', async () => {
        const randomizedEvaluation = { ...mockEvaluation, RandomizeQuestions: true }
        
        const mockEvaluationQuestions = [
            {
                QuestionId: 'q1',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 1
            },
            {
                QuestionId: 'q2',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 2,
                SectionDisplayIndex: 1
            },
            {
                QuestionId: 'q3',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 2
            }
        ]

        const mockFixedQuestions = [
            { Id: 'q1', Version: 1 },
            { Id: 'q2', Version: 1 },
            { Id: 'q3', Version: 1 }
        ]

        let callCount = 0
        const queryStub = Sinon.stub().callsFake(() => {
            callCount++
            if (callCount === 1) {
                return Promise.resolve({ recordset: mockEvaluationQuestions })
            } else if (callCount === 2) {
                return Promise.resolve({ recordset: [mockFixedQuestions[0]] }) // q1
            } else if (callCount === 3) {
                return Promise.resolve({ recordset: [mockFixedQuestions[1]] }) // q2
            } else if (callCount === 4) {
                return Promise.resolve({ recordset: [mockFixedQuestions[2]] }) // q3
            }
            return Promise.resolve({ recordset: [] })
        })

        // Mock Math.random to ensure predictable "randomization"
        const originalRandom = Math.random
        Math.random = Sinon.stub().returns(0.7) // Forces reverse order

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(randomizedEvaluation)
        
        expect(result).to.have.length(3)
        // Section order should be preserved (section 1 before section 2)
        // But questions within sections should be randomized
        const section1Questions = result.filter((q: QuestionWithDisplayInfo) => q.SectionDisplayIndex === 1)
        const section2Questions = result.filter((q: QuestionWithDisplayInfo) => q.SectionDisplayIndex === 2)
        
        expect(section1Questions).to.have.length(2)
        expect(section2Questions).to.have.length(1)
        
        // Restore Math.random
        Math.random = originalRandom
    })

    it('handles empty evaluation questions gracefully', async () => {
        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: [] })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)
        
        expect(result).to.be.an('array')
        expect(result).to.have.length(0)
    })

    it('prevents duplicate questions across sections', async () => {
        const duplicateQuestionId = uuid()
        const mockEvaluationQuestions = [
            {
                QuestionId: duplicateQuestionId,
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 1
            },
            {
                QuestionId: duplicateQuestionId,
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 2
            }
        ]

        const mockFixedQuestions = [
            { Id: duplicateQuestionId, Version: 1 }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockEvaluationQuestions })
            .onSecondCall().resolves({ recordset: mockFixedQuestions })
            .onThirdCall().resolves({ recordset: mockFixedQuestions })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)
        
        expect(result).to.have.length(1) // Should only include the question once
        expect(result[0].Id).to.equal(duplicateQuestionId)
    })

    it('handles mixed fixed and dynamic questions', async () => {
        const mockEvaluationQuestions = [
            {
                QuestionId: 'fixed-q1',
                QuestionVersion: 1,
                QuestionSetId: null,
                DisplayIndex: 1,
                SectionDisplayIndex: 1
            },
            {
                QuestionSetId: uuid(),
                QuestionSetVersion: 1,
                SubsetSize: 1,
                DisplayIndex: 2,
                SectionDisplayIndex: 1
            }
        ]

        const mockFixedQuestions = [
            { Id: 'fixed-q1', Version: 1 }
        ]

        const mockDynamicQuestions = [
            { Id: 'dynamic-q1', Version: 1 }
        ]

        const queryStub = Sinon.stub()
            .onFirstCall().resolves({ recordset: mockEvaluationQuestions })
            .onSecondCall().resolves({ recordset: mockFixedQuestions })
            .onThirdCall().resolves({ recordset: mockDynamicQuestions })

        const assessmentGenerator = await esmock('./assesment-generator-service.js', {
            '@lcs/mssql-utility': {
                default: {
                    getPool: () => ({
                        request: () => ({
                            input: Sinon.stub().returnsThis(),
                            query: queryStub
                        })
                    })
                }
            },
            '../mssql/question-options/get-for-question.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../mssql/questions/get-child-questions.service.js': {
                default: Sinon.stub().resolves([])
            }
        })

        const result = await assessmentGenerator.default(mockEvaluation)
        
        expect(result).to.have.length(2)
        expect(result[0].Id).to.equal('fixed-q1')
        expect(result[1].Id).to.equal('dynamic-q1')
    })
})


