jslVersion = 'v2.5.0'
library identifier: "jsl@${jslVersion}", retriever: modernSCM(github(apiUri: 'https://github.northgrum.com/api/v3',traits: [gitHubBranchDiscovery(1)], repository: 'jen<PERSON>-shared-library', repoOwner: 'missionsoftwarefactory'))

// NG Repos
final PROXY_REPO = "docker-ng-untrusted-group.northgrum.com"
final STAGING_REPO = "docker-ng-staging.northgrum.com"
final RELEASE_REPO = "docker-ng-releases.northgrum.com"
// App Info
final IMAGE_NAMESPACE = "lcs/tess-f"
final IMAGE_NAME = "evaluations-engine-server"
// Branch Info
final MAIN_BRANCH = 'master'
final DEVELOP_BRANCH = 'development'
final RELEASE_BRANCH_PREFIX = 'release/'
// Credentials
final NEXUS_CREDENTIALS = 'nexus-repo'
final SONARQUBE_CREDENTIALS = 'sonar-token'
final LIFECYCLE_CREDENTIALS = 'lifecycle-cred'
final KUBECONFIG_FILE = 'dev-kubeconfig'
final STAGE_KUBECONFIG_FILE = 'tess-dev-stage.ekho-prod.config'
final COSIGN_CREDENTIALS = 'cosign-pass'
// Container Signing Private key
final PRIVATE_KEY = '/etc/keys/signing/cosign.key'
// image publishing tag (to be set in the labels stage)
def IMAGE_TAG = "dev-latest" // default to the dev latest image
// publishing repo
def PUBLISH_REPO = STAGING_REPO
// publishing flags
def PUBLISH_LATEST = false // production release
def PUBLISH_STAGE_LATEST = false // staging release
// lifecycle scan stage
final IQ_STAGE_RELEASE = 'release'
final IQ_STAGE_STAGING = 'stage-release'
final IQ_STAGE_SOURCE = 'source'
def IQ_STAGE = IQ_STAGE_SOURCE
// agent label
final AGENT_LABEL = 'LC1'

pipeline {
  agent {
    label AGENT_LABEL
  }

  options {
    disableConcurrentBuilds(abortPrevious: true)
    buildDiscarder(logRotator(numToKeepStr: "10", artifactNumToKeepStr: "3"))
    timeout(time: 60, unit: "MINUTES")
    timestamps()
  }

  environment {
    no_proxy = "$no_proxy,************/24,***********/24,api.ocpshareddev.gc1.myngc.com,ocpshareddev-pswfc-int-70be69b7e8a89a5a.elb.us-gov-west-1.amazonaws.com"
    COSIGN_PASSWORD=credentials("${COSIGN_CREDENTIALS}")
  }

  stages {
    stage('Build Labels') {
      steps {
        echo '[INFO] Preparing build labels'
        script {
          if(env.TAG_NAME) {
            // we are building a release tag
            PUBLISH_REPO = RELEASE_REPO
            IMAGE_TAG = env.TAG_NAME
            IQ_STAGE = IQ_STAGE_RELEASE

            def latest_tag = sh(script: "git describe --tags `git rev-list --tags --max-count=1`", returnStdout: true).trim()
            if(env.TAG_NAME == latest_tag) {
              echo "[INFO] ${env.TAG_NAME} is the most recent tag.  Tagging docker image with 'latest'."
              PUBLISH_LATEST = true
            }

            def rc = sh(script: "docker pull ${PROXY_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG} > /dev/null 2>&1", returnStatus: true)
            if (rc == 0) {
              echo "[INFO] Image ${PROXY_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG} already exists."
              def time_for_tag = new Date().format("yyyyMMdd'T'HHmmss", TimeZone.getTimeZone('UTC'))
              IMAGE_TAG = env.TAG_NAME + '-' + time_for_tag
              PUBLISH_REPO = STAGING_REPO
            }
          } else if (env.BRANCH_NAME.indexOf(RELEASE_BRANCH_PREFIX) != -1) {
            // we are preparing a release
            PUBLISH_STAGE_LATEST = true
            IMAGE_TAG = "${env.BRANCH_NAME.replace('release/', '')}-rc${env.BUILD_NUMBER}"
            IQ_STAGE = IQ_STAGE_STAGING
          }
          echo "[INFO] Creating ${PROXY_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}."
        }
      }
    }

    stage ('Preparation') {
      steps {
        sh 'npm i'
      }
    }

    stage ('Unit Test') {
      steps {
        echo 'TODO: Unit Test App'
      }
    }

    stage ('SonarQube Quality Scan') {
      when {
        expression { true }
      }
      steps {
        sonarqubeAnalysis([credentialsID: SONARQUBE_CREDENTIALS, path: '/opt/sonar-scanner-5.0.1.3006-linux/bin'])
        sonarqubeQualityGate([abortPipeline: false])
      }
    }

    stage ('Build') {
      environment {
        HOME = '.' // workaround npm permissions
      }
      steps {
        sh 'npm run build'
        // rebuild node modules but with only production dependencies
        sh 'npm ci --omit=dev'
      }
    }

    stage ('Package') {
      when {
        anyOf {
          branch DEVELOP_BRANCH
          branch "${RELEASE_BRANCH_PREFIX}*"
          tag "v*.*.*"
        }
      }
      steps {
        withCredentials([usernamePassword(credentialsId: NEXUS_CREDENTIALS, passwordVariable: 'NXRM_PW', usernameVariable: 'NXRM_UN')]) {
          cmd("docker login --username=${NXRM_UN} --password=${NXRM_PW} ${PROXY_REPO}")
          dockerBuildImage([
            imageName: IMAGE_NAME,
            imageVersion: IMAGE_TAG
          ])
          sh "docker logout ${PROXY_REPO}"
        }
      }
    }

    stage ('Publish') {
      when {
        anyOf {
          branch DEVELOP_BRANCH
          branch "${RELEASE_BRANCH_PREFIX}*"
          tag "v*.*.*"
        }
      }
      steps {
        script {
          dockerPublishImage([
            imageName: IMAGE_NAME,
            imageVersion: IMAGE_TAG,
            namespace: IMAGE_NAMESPACE,
            registryServer: PUBLISH_REPO,
            credential: NEXUS_CREDENTIALS,
            removeImage: false
          ])

          if (PUBLISH_LATEST) {
            echo "[INFO] Updating latest tag to ${STAGING_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:latest"
            sh "docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_NAME}:latest"
            dockerPublishImage([
              imageName: IMAGE_NAME,
              imageVersion: "latest",
              namespace: IMAGE_NAMESPACE,
              registryServer: STAGING_REPO,
              credential: NEXUS_CREDENTIALS,
              removeImage: true
            ])
          } else if (PUBLISH_STAGE_LATEST) {
            echo "[INFO] Updating latest staging tag to ${STAGING_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:staging-latest"
            sh "docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_NAME}:staging-latest"
            dockerPublishImage([
              imageName: IMAGE_NAME,
              imageVersion: "staging-latest",
              namespace: IMAGE_NAMESPACE,
              registryServer: STAGING_REPO,
              credential: NEXUS_CREDENTIALS,
              removeImage: true
            ])
          }
        }
      }
      post {
        always {
          sh "docker rmi ${IMAGE_NAME}:${IMAGE_TAG}"
          sh "docker rmi ${PUBLISH_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
        }
      }
    }

    stage ('Signature') {
      when {
        anyOf {
          branch "${RELEASE_BRANCH_PREFIX}*"
          tag "v*.*.*"
        }
      }
      steps {
        script{
          withCredentials([usernamePassword(credentialsId: NEXUS_CREDENTIALS, passwordVariable: 'NXRM_PW', usernameVariable: 'NXRM_UN')]) {
            sh "docker login --username=${NXRM_UN} --password=${NXRM_PW} ${PUBLISH_REPO}"

            // cosign requires image digest for signing
            def digest = sh(script: "skopeo inspect --format '{{.Digest}}' docker://${PUBLISH_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}", returnStdout: true)
            echo "digest: ${digest}"

            def rc = sh(script: "cosign sign --recursive --tlog-upload=false --key ${PRIVATE_KEY} ${PUBLISH_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}@${digest} > /dev/null 2>&1", returnStatus: true)
            if (rc == 0) {
                echo "[INFO] Signed image ${PUBLISH_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}@${digest}."
            } else {
                echo "[ERROR] Failed to sign image ${PUBLISH_REPO}/${IMAGE_NAMESPACE}/${IMAGE_NAME}@${digest}."
            }
            sh "docker logout ${PUBLISH_REPO}"
          }
        }
      }
    }

    stage ('Deploy Dev') {
      when {
        branch DEVELOP_BRANCH
      }
      options {
        skipDefaultCheckout true
      }
      steps {
        withCredentials([file(credentialsId: KUBECONFIG_FILE, variable: 'FILE')]) {
          sh 'kubectl delete pod -l app=evaluation-engine-server --kubeconfig "${FILE}"'
        }
      }
    }

    stage ('Deploy Stage') {
      when {
        branch "${RELEASE_BRANCH_PREFIX}*"
      }
      options {
        skipDefaultCheckout true
      }
      steps {
        withCredentials([file(credentialsId: STAGE_KUBECONFIG_FILE, variable: 'FILE')]) {
          sh 'kubectl delete pod -l app=evaluation-engine-server --kubeconfig $FILE'
        }
      }
    }

    stage ('Lifecycle Scan') {
        steps {
          nexusPolicyEvaluation iqScanPatterns: [
            [scanPattern: '**/npm-shrinkwrap.json' ], 
            [scanPattern: '**/package-lock.json']
          ], 
          enableDebugLogging: false, 
          failBuildOnNetworkError: false, 
          iqApplication: selectedApplication('EVAL_Engine_Server'), 
          iqInstanceId: 'NexusIQServer', 
          iqOrganization: '54d716745f1840c6b4f019f35609a975', 
          iqStage: IQ_STAGE, 
          jobCredentialsId: LIFECYCLE_CREDENTIALS
        }
      }
  }

  post {
    always {
      postBuildActions()
      cleanWs()
    }
  }
}