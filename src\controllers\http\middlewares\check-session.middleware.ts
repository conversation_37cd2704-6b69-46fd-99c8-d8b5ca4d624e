import type { NextFunction, Request, Response } from 'express'
import logger from '@lcs/logger'
import sessionAuthority from '@lcs/session-authority'
import settings from '../../../config/settings.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-MIddleware.check-session', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction): Promise<void> {
  if (settings.sessionAuthority.bypass) {
    req.session = {
      userId: req.cookies.userId,
      sessionId: ''
    }
    next()
    return
  }

  const session = await sessionAuthority.authenticate(req, res)

  if (session.result === 'valid') {
    // successful. go on to the next function in the chain
    next()
  } else {
    // Invalid session! Redirect to the login page
    log('error', 'Received invalid session', { session, success: false, req })
    res.redirect('/login')
  }
}
