import mssql from '@lcs/mssql-utility'
import { QuestionWithOptions } from '../../models/internal/question-with-options.js'
import {EvaluationQuestion, EvaluationQuestionTableName, EvaluationQuestionFields} from '@tess-f/sql-tables/dist/evaluations/evaluation-questions.js'
import { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import getOptionsForQuestion from '../mssql/question-options/get-for-question.service.js'
import getChildrenForQuestion from '../mssql/questions/get-child-questions.service.js'
import { QuestionVersion } from '@tess-f/sql-tables/dist/evaluations/question-version.js'
import { DynamicQuestionSetVersionTableName} from '@tess-f/sql-tables/dist/evaluations/dynamic-question-set-version.js'
import { QuestionVersionViewName } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { QuestionBankQuestionFields, QuestionBankQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/question-bank-question.js'
import { EvaluationQuestionBankFields, EvaluationQuestionBanksTableName } from '@tess-f/sql-tables/dist/evaluations/evaluation-question-bank.js'

type QuestionWithDisplayOrder = QuestionWithOptions & {
  DisplayIndex?: number
}
export default async function assessmentGenerator (evaluation: CurrentEvaluationView): Promise<QuestionWithOptions[]> {

  const request = mssql.getPool().request()
  request.input('evalId', evaluation.Id)
  request.input('evalVersion', evaluation.Version)

  // Get all evaluation questions with section information
  const evaluationQuestionsResults = await request.query<EvaluationQuestion & { SubsetSize: number | null, SectionDisplayIndex: number }>(`
    SELECT DISTINCT eq.*, dqsv.SubsetSize, sv.DisplayIndex as SectionDisplayIndex
    FROM [${EvaluationQuestionTableName}] eq
    LEFT JOIN [${DynamicQuestionSetVersionTableName}] dqsv ON dqsv.QuestionSetId = eq.[${EvaluationQuestionFields.QuestionSetId}]
      AND dqsv.QuestionSetVersion = eq.QuestionSetVersion
    LEFT JOIN EVAL_SectionVersions sv ON sv.SectionId = eq.SectionId 
      AND sv.Version = eq.SectionVersion
    WHERE eq.[${EvaluationQuestionFields.EvaluationId}] = @evalId 
      AND eq.[${EvaluationQuestionFields.EvaluationVersion}] = @evalVersion 
    ORDER BY sv.DisplayIndex, eq.[${EvaluationQuestionFields.DisplayIndex}]
  `)


  const combinedQuestions: (QuestionWithDisplayOrder & { SectionDisplayIndex: number })[] = []
  const usedQuestionIds = new Set<string>()

  // Process each fixed & dynamic question set
  for (const evalQuestion of evaluationQuestionsResults.recordset) {
    
    // Handle Fixed Questions (no QuestionSetId)
    if (!evalQuestion.QuestionSetId) {
      request.input('questionId', evalQuestion.QuestionId)
      request.input('questionVersion', evalQuestion.QuestionVersion)
      
      const fixedQuestionsQuery = await request.query<QuestionVersion>(`
        SELECT DISTINCT qv.*
        FROM [${QuestionVersionViewName}] qv
        JOIN [${QuestionBankQuestionsTableName}] qbq ON
          qbq.[${QuestionBankQuestionFields.QuestionId}] = qv.Id
          AND qbq.[${QuestionBankQuestionFields.QuestionVersion}] = qv.Version
        JOIN [${EvaluationQuestionBanksTableName}] eqb ON
          eqb.[${EvaluationQuestionBankFields.QuestionBankId}] = qbq.[${QuestionBankQuestionFields.QuestionBankId}]
          AND eqb.[${EvaluationQuestionBankFields.QuestionBankVersion}] = qbq.[${QuestionBankQuestionFields.QuestionBankVersion}]
          AND eqb.[${EvaluationQuestionBankFields.EvaluationId}] = @evalId
          AND eqb.[${EvaluationQuestionBankFields.EvaluationVersion}] = @evalVersion
        WHERE qv.Id = @questionId AND qv.Version = @questionVersion
      `)

      for (const question of fixedQuestionsQuery.recordset) {
        if (!usedQuestionIds.has(question.QuestionId!)) {
          usedQuestionIds.add(question.QuestionId!)
          
          const subQuestions = await getChildrenForQuestion(question.QuestionId!, question.Version!)
          const questionWithOptions = {
            ...question,
            DisplayIndex: evalQuestion.DisplayIndex,
            SectionDisplayIndex: evalQuestion.SectionDisplayIndex,
            Options: await getOptionsForQuestion(question.QuestionId!, question.Version!),
            SubQuestions: await Promise.all(subQuestions.map(async subQuestion => ({
              ...subQuestion,
              Options: await getOptionsForQuestion(subQuestion.Id ?? '', subQuestion.Version ?? 1)
            })))
          }
          
          combinedQuestions.push(questionWithOptions)
        }
      }
    } else {
      // Handle Dynamic Questions (has QuestionSetId)
      const subsetSize = evalQuestion.SubsetSize
      const questionSetId = evalQuestion.QuestionSetId
      const dynamicRequest = mssql.getPool().request()
      dynamicRequest.input('questionSetId', questionSetId)
      dynamicRequest.input('questionSetVersion', evalQuestion.QuestionSetVersion)
      
      const dynamicQuestionsQuery = `
        WITH DynamicQuestions AS (
          -- From QuestionBanks
          SELECT DISTINCT [${QuestionVersionViewName}].*, dqsqb.QuestionSetId, dqsqb.QuestionSetVersion
          FROM EVAL_DynamicQuestionSets_QuestionBanks dqsqb
          JOIN EVAL_QuestionBankQuestions qbq ON qbq.QuestionBankId = dqsqb.QuestionBankId 
            AND qbq.QuestionBankVersion = dqsqb.QuestionBankVersion
          JOIN [${QuestionVersionViewName}] ON [${QuestionVersionViewName}].Id = qbq.QuestionId 
            AND [${QuestionVersionViewName}].Version = qbq.QuestionVersion
          WHERE dqsqb.QuestionSetId = @questionSetId AND dqsqb.QuestionSetVersion = @questionSetVersion

          UNION

          -- From QuestionTypes  
          SELECT DISTINCT [${QuestionVersionViewName}].*, dqsqt.QuestionSetId, dqsqt.QuestionSetVersion
          FROM EVAL_DynamicQuestionSets_QuestionTypes dqsqt
          JOIN [${QuestionVersionViewName}] ON [${QuestionVersionViewName}].QuestionTypeId = dqsqt.QuestionTypeId
          WHERE dqsqt.QuestionSetId = @questionSetId AND dqsqt.QuestionSetVersion = @questionSetVersion

          UNION

          -- From Objectives
          SELECT DISTINCT [${QuestionVersionViewName}].*, dqso.QuestionSetId, dqso.QuestionSetVersion  
          FROM EVAL_DynamicQuestionSets_Objectives dqso
          JOIN [${QuestionVersionViewName}] ON [${QuestionVersionViewName}].ObjectiveId = dqso.ObjectiveId
          WHERE dqso.QuestionSetId = @questionSetId AND dqso.QuestionSetVersion = @questionSetVersion
        )
        SELECT DISTINCT * FROM DynamicQuestions
        ${evaluation.RandomizeQuestions ? 'ORDER BY NEWID()' : ''}
      `

      const questionResults = await dynamicRequest.query(dynamicQuestionsQuery)
      const availableQuestions = questionResults.recordset.filter(q => !usedQuestionIds.has(q.Id))
      const selectedQuestions = subsetSize ? availableQuestions.slice(0, subsetSize) : availableQuestions

      for (const question of selectedQuestions) {
        usedQuestionIds.add(question.Id)
        
        const subQuestions = await getChildrenForQuestion(question.Id, question.Version)
        const questionWithOptions = {
          ...question,
          DisplayIndex: evalQuestion.DisplayIndex,
          SectionDisplayIndex: evalQuestion.SectionDisplayIndex,
          Options: await getOptionsForQuestion(question.Id, question.Version),
          SubQuestions: await Promise.all(subQuestions.map(async subQuestion => ({
            ...subQuestion,
            Options: await getOptionsForQuestion(subQuestion.Id ?? '', subQuestion.Version ?? 1)
          })))
        }
        
        combinedQuestions.push(questionWithOptions)
      }
    }
  }

  // Group questions by section
  const questionsBySection = new Map<number, typeof combinedQuestions>()
  combinedQuestions.forEach(question => {
    const sectionIndex = question.SectionDisplayIndex ?? 0
    if (!questionsBySection.has(sectionIndex)) {
      questionsBySection.set(sectionIndex, [])
    }
    questionsBySection.get(sectionIndex)!.push(question)
  })

  // Sort and randomize within sections if needed
  // Section order is preserved but questions may be scrambled
  const sortedQuestions: QuestionWithOptions[] = []
  const sortedSections = Array.from(questionsBySection.entries()).sort(([a], [b]) => a - b)
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  for (const [sectionIndex, sectionQuestions] of sortedSections) {
    if (evaluation.RandomizeQuestions) {
      // Randomize questions within section but keep section order
      const shuffled = [...sectionQuestions].sort(() => Math.random() - 0.5)
      sortedQuestions.push(...shuffled)
    } else {
      // Sort by DisplayIndex within section
      const sorted = sectionQuestions.toSorted((a, b) => (a.DisplayIndex ?? 0) - (b.DisplayIndex ?? 0))
      sortedQuestions.push(...sorted)
    }
  }

  return sortedQuestions
}
