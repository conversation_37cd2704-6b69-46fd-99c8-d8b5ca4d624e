{"name": "evaluations-engine", "version": "1.0.2", "description": "Engine for all things taking and exploring evaluations (not authoring, that is handled by the authoring server)", "main": "index.js", "type": "module", "scripts": {"start": "node build/index.js", "start:dev": "nodemon --inspect", "build": "rimraf ./build && tsc && npm run copy-files", "copy-files": "copyfiles -u 1 src/**/*.txt build/", "test": "ts-mocha src/**/*.spec.ts --inspect --exit", "test:coverage": "nyc --reporter=lcov --reporter=text --reporter=html --reporter=text-summary npm test"}, "repository": {"type": "git", "url": "https://github.northgrum.com/LCS-TESS/Eval-Engine.git"}, "keywords": ["Express", "TypeScript", "ES", "<PERSON><PERSON>", "API"], "author": "Northrop Grumman", "license": "Northrop Grumman", "dependencies": {"@lcs/logger": "^4.0.3", "@lcs/mssql-utility": "^3.0.0", "@lcs/rabbitmq": "^4.0.2", "@lcs/session-authority": "^4.0.0", "@tess-f/backend-utils": "^2.0.2", "@tess-f/shared-config": "^2.0.10", "@tess-f/sql-tables": "^2.3.17", "@tess-f/system-config": "^2.0.3", "cookie-parser": "^1.4.7", "esmock": "^2.7.0", "express": "^5.1.0", "http-status": "^2.1.0", "prettyjson": "^1.2.5", "prom-client": "^15.1.3", "redis": "^5.5.6", "string-strip-html": "^13.4.12", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/chai": "^5.2.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/mocha": "^10.0.10", "@types/mssql": "^9.1.7", "@types/node": "^22.14.1", "@types/prettyjson": "^0.0.33", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "chai": "^5.2.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^9.29.0", "globals": "^16.2.0", "mocha": "^11.7.2", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.10", "nyc": "^17.1.0", "rimraf": "^6.0.1", "sinon": "^21.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}, "overrides": {"body-parser": "^1.20.3"}}