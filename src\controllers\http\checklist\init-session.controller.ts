import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import { RedisClient } from '../../../services/redis/client.service.js'
import httpStatus from 'http-status'
import { createHash } from 'crypto'
import { v4 as uuid } from 'uuid'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'

const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
const log = logger.create('Controller-HTTP.initialize-checklist-session', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try{

    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const { userIds } = z.object({ userIds: zodGUID.array().nonempty() }).parse(req.body)

    // initialize the session
    // in the future we may just create a record in the database and use that as the session id
    const hasher = createHash('sha256')
    hasher.update(Buffer.from(uuid()).toString('base64'))
    const sessionId = hasher.digest('hex')

    // Store the user ids in redis
    await RedisClient.setChecklistUsers(sessionId, id, userIds)
    log('info', 'Successfully initialized checklist session', { success: true, sessionId, req })

    res.json(sessionId)
  }
    catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to initialize checklist session', { success: false, error, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
