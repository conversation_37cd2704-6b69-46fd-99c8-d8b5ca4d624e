import { EvaluationVersion } from '@tess-f/sql-tables/dist/evaluations/evaluation-version.js'
import { QuestionWithOptions } from '../../models/internal/question-with-options.js'

export default function pickQuestions (allQuestions: QuestionWithOptions[], options: Required<Pick<EvaluationVersion, 'EqualizeObjectives' | 'EqualizeQuestions' | 'IncludeAllQuestions' | 'NumberOfQuestions'>>): QuestionWithOptions[] {
  if (options.IncludeAllQuestions) {
    // use all the questions
    return allQuestions.slice()
  }

  if (allQuestions.length <= (options.NumberOfQuestions ?? allQuestions.length)) {
    // we don't have enough questions to pick from so just use them all
    return allQuestions.slice()
  }

  // TODO: pick by objectives, question types, etc
  return allQuestions.slice()
}
