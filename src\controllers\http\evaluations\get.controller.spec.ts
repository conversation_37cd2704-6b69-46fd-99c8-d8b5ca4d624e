import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'


describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
            },
            '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                default: Sinon.stub().returns(Promise.resolve([ ]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
            },
            '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                default: Sinon.stub().returns(Promise.resolve([ ]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('id')
        expect(data).to.include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
            },
            '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([ ]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
   

})