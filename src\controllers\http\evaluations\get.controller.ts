import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import httpStatus from 'http-status'
import getEvaluation from '../../../services/mssql/evaluations/get.service.js'
import getAllQuestionsForEvaluation from '../../../services/mssql/questions/get-all-for-evaluation.service.js'
import pickQuestions from '../../../services/internal/question-picker-service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'

const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
const log = logger.create('Controller-HTTP.get-evaluation', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {

    const { id } = z.object({
        id: zodGUID
      }).parse(req.params) 


    // Get the evaluation
    const evaluation = await getEvaluation(id)
    log('info', 'Successfully fetched evaluation', { evalId: evaluation.Id, success: true, req })

    // Get all questions from all banks for evaluation
    const allQuestions = await getAllQuestionsForEvaluation(evaluation.Id ?? '', evaluation.Version ?? 1)
    log('info', 'Successfully fetched all questions for evaluation', { success: true, count: allQuestions.length, req })

    // pick questions
    const evalQuestions = pickQuestions(allQuestions, { EqualizeObjectives: evaluation.EqualizeObjectives ?? false, EqualizeQuestions: evaluation.EqualizeQuestions ?? false, IncludeAllQuestions: evaluation.IncludeAllQuestions ?? false, NumberOfQuestions: evaluation.NumberOfQuestions ?? allQuestions.length })
    log('info', 'Successfully selected questions for evaluation', { count: evalQuestions.length, success: true, req })

    // set display order
    if (!evaluation.RandomizeQuestions) {
      evalQuestions.sort((a, b) => (a.OrderId ?? 1) - (b.OrderId ?? 1))
    }
    // if assesment we have some speciel requirments



    // TODO: randomize the order of the questions

    // remove correct response option, this is accomplished by setting the correct filed on options to false
    evalQuestions.forEach(question => {
      question.Options.forEach(option => { option.Correct = undefined })
      if (question.SubQuestions) {
        question.SubQuestions.forEach(subQuestion => {
          subQuestion.Options.forEach(option => { option.Correct = undefined })
        })
      }
    })
    log('verbose', 'Set the option response correct field to undefined', { success: true, req })

    // send the data to the requester
    res.json({ evaluation, questions: evalQuestions })
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to get evaluation data', { error, success: false, evalId: req.params.id, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
