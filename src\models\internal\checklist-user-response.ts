import { EvaluationSession } from '@tess-f/sql-tables/dist/evaluations/session.js'
import { QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'
import z from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

export type ChecklistUserResponse =
  Required<Pick<EvaluationSession, 'UserId' | 'Passed'>> &
  Pick<EvaluationSession, 'Notes'> &
  {
    Responses: Array<Required<Pick<QuestionResponse, 'QuestionId' | 'QuestionVersion'>> & Pick<QuestionResponse, 'Duration' | 'Notes' | 'OptionId' | 'OptionVersion'>>
  }

export const checklistUserResponseSchema = z.object({
  UserId: zodGUID,
  Passed: z.boolean(),
  Notes: z.string().optional(),
  Responses: z.array(
    z.object({
      QuestionId: zodGUID,
      QuestionVersion: z.number(),
      Duration: z.string().duration().optional(),
      Notes: z.string().optional(),
      OptionId: zodGUID.optional(),
      OptionVersion: z.number().optional()
    }).superRefine(({ OptionId, OptionVersion }, ctx) => {
      if (OptionId && !OptionVersion) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'OptionId provided without OptionVersion', path: ['OptionVersion'] })
      }
    })
  )
})
