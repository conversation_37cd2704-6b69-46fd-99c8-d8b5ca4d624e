import { createClient, RedisClientType } from 'redis'
import logger from '@lcs/logger'
import settings from '../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-Redis.client')

const CHECKLIST_USERS_CLIENT = 'checklist-users'
const REDIS_NOT_INITIALIZED = 'Redis client is not initialized'

/**
 * A singleton instance of Redis clients for various databases
 */
export class RedisClient {
  private static _instance?: RedisClient
  private readonly _clients = new Map<string, RedisClientType>()

  private static async getInstance (): Promise<RedisClient> {
    if (!RedisClient._instance) {
      await RedisClient.initInstance()
    }
    return RedisClient._instance!
  }

  private static async initInstance (): Promise<void> {
    // Create client connection
    try {
      RedisClient._instance = new RedisClient()
      const client: RedisClientType = createClient({
        url: settings.redis.url,
        password: settings.redis.password,
        database: settings.redis.checklistUsersDatabase
      })
      await client.connect().then(() => log('info', 'Redis client connected.', { success: false }))
      client.on('error', (error) => log('error', 'Redis client error', { errorMessage: getErrorMessage(error) }))
      RedisClient._instance._clients.set(CHECKLIST_USERS_CLIENT, client)
    } catch (error) {
      log('error', 'Failed to initialize redis client', { errorMessage: getErrorMessage(error), success: false })
    }
  }

  public static async shutdown (): Promise<void> {
    const instance = await RedisClient.getInstance()
    for (const key of instance._clients.keys()) {
      const client = instance._clients.get(key)
      if (client) {
        await client.quit()
        instance._clients.delete(key)
      }
    }
    RedisClient._instance = undefined
  }

  public static async setChecklistUsers (sessionId: string, checklistId: string, userIds: string[]): Promise<void> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(CHECKLIST_USERS_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: CHECKLIST_USERS_CLIENT, success: false })
      return
    }

    await client.set(sessionId, `${checklistId}:${userIds.join(',')}`)
    await client.expire(sessionId, 300)
  }

  public static async getChecklistUsers (sessionId: string): Promise<{ checklistId: string, userIds: string[] }> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(CHECKLIST_USERS_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: CHECKLIST_USERS_CLIENT, success: false })
      throw new Error(REDIS_NOT_INITIALIZED)
    }

    const result = await client.getDel(sessionId)

    if (result === null) {
      log('warn', 'Checklist users not found, session expired or does not exist', { success: false })
      throw new Error('Session invalid')
    }

    log('info', 'Successfully fetched checklist user list', { success: true, sessionId })
    const parts = result.split(':')
    return { checklistId: parts[0], userIds: parts[1].split(',') }
  }

  public static async setUserSessionData (sessionId: string, userSessionMap: Map<string, string>): Promise<void> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(CHECKLIST_USERS_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: CHECKLIST_USERS_CLIENT, success: false })
      return
    }

    const userSession: Record<string, string> = {}
    userSessionMap.forEach((value, key) => {
      userSession[key] = value
    })

    log('debug', 'Setting user session data in redis', { sessionId, userSession })

    await client.set(sessionId, JSON.stringify(userSession))
  }

  public static async getUserSessionData (sessionId: string): Promise<Map<string, string>> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(CHECKLIST_USERS_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: CHECKLIST_USERS_CLIENT, success: false })
      throw new Error(REDIS_NOT_INITIALIZED)
    }

    const result = await client.get(sessionId)

    if (result === null) {
      log('warn', 'User session data not found, session expired or does not exist', { success: false })
      throw new Error('Session invalid')
    }

    client.del(sessionId)

    log('info', 'Successfully fetched user session data', { success: true, sessionId, result })

    const userSession: Record<string, string> = JSON.parse(result)
    return new Map(Object.entries(userSession))
  }
}
