import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { SessionModel } from '../../../models/session.model.js'

describe('HTTP get session data controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-session-data.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getChecklistUsers: Sinon.stub().resolves({checklistID: uuid(), userIds: [uuid(), uuid()]}),
                    setUserSessionData: Sinon.stub().resolves()
                }
            },
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
           '../../../services/amqp/system/get-system-config.service.js': {
            getSystemConfig: Sinon.stub().returns(Promise.resolve({domain: 'localhost'}))

           },
           '../../../services/mssql/user/get.service.js': {
            default: Sinon.stub().returns(Promise.resolve({FirstName: 'Test', LastName: 'Test', Username: 'Test'}))
           },
           '../../../services/mssql/session/create.service.js': {
            default: Sinon.stub().returns(Promise.resolve(new SessionModel({ Id: uuid() })))
           },
           '../../../services/amqp/lrs/create-statement.service.js': {
            default: Sinon.stub().returns(Promise.resolve(''))
           }    
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-session-data.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getChecklistUsers: Sinon.stub().resolves({checklistID: uuid(), userIds: [uuid(), uuid()]}),
                    setUserSessionData: Sinon.stub().resolves()
                }
            },
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
           '../../../services/amqp/system/get-system-config.service.js': {
            getSystemConfig: Sinon.stub().returns(Promise.resolve({domain: 'localhost'}))

           },
           '../../../services/mssql/user/get.service.js': {
            default: Sinon.stub().returns(Promise.resolve({FirstName: 'Test', LastName: 'Test', Username: 'Test'}))
           },
           '../../../services/mssql/session/create.service.js': {
            default: Sinon.stub().returns(Promise.resolve(new SessionModel({ Id: uuid() })))
           },
           '../../../services/amqp/lrs/create-statement.service.js': {
            default: Sinon.stub().returns(Promise.resolve(''))
           }    
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('id')
        expect(data).to.include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-session-data.controller', {
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getChecklistUsers: Sinon.stub().resolves({checklistID: uuid(), userIds: [uuid(), uuid()]}),
                    setUserSessionData: Sinon.stub().resolves()
                }
            },
            '../../../services/mssql/evaluations/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({}))
            },
           '../../../services/amqp/system/get-system-config.service.js': {
            getSystemConfig: Sinon.stub().rejects(Promise.resolve({domain: 'localhost'}))

           },
           '../../../services/mssql/user/get.service.js': {
            default: Sinon.stub().rejects(Promise.resolve({FirstName: 'Test', LastName: 'Test', Username: 'Test'}))
           },
           '../../../services/mssql/session/create.service.js': {
            default: Sinon.stub().rejects(Promise.resolve(new SessionModel({ Id: uuid() })))
           },
           '../../../services/amqp/lrs/create-statement.service.js': {
            default: Sinon.stub().rejects(Promise.resolve(''))
           }    
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
    


})